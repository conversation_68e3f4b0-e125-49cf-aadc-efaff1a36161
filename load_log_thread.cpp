#include "load_log_thread.h"

LoadLogThread::LoadLogThread(QObject* parent)
    : QObject(parent)
{
}

void LoadLogThread::slot_start_work()
{
    QFile file(path);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        // QMessageBox::warning(this, tr("错误"), tr("无法打开文件: %1").arg(path));
        return;
    }

    MyTableModelBig mode =

    // 清空现有数据
    ui->ITV_csv->delete_all_row();

    // 启用超级内存模式
    ui->ITV_csv->get_table_model()->enableUltraMemoryMode(true);

    QTextStream in(&file);
    // 尝试自动检测UTF-16/UTF-8 BOM
    in.setAutoDetectUnicode(true);
    //
    QString line;
    bool    first_line = true;
    int     row_count  = 0;

    while (!in.atEnd()) {
        line = in.readLine();
        if (line.trimmed().isEmpty()) {
            continue;
        }

        QStringList fields = line.split(',');

        if (first_line) {
            // 处理表头
            QVector<QString> headers;
            for (const QString& field : fields) {
                QString cleaned_field = field.trimmed();
                if (cleaned_field.startsWith('"') && cleaned_field.endsWith('"')) {
                    cleaned_field = cleaned_field.mid(1, cleaned_field.length() - 2);
                }
                headers.append(cleaned_field);
            }
            ui->ITV_csv->set_title_list(headers);
            first_line = false;
        }
        else {
            // 处理数据行 - 使用优化存储
            QVector<CellData> row_data;
            for (const QString& field : fields) {
                QString cleaned_field = field.trimmed();
                if (cleaned_field.startsWith('"') && cleaned_field.endsWith('"')) {
                    cleaned_field = cleaned_field.mid(1, cleaned_field.length() - 2);
                }
                // 自动检测数据类型并创建优化的CellData
                row_data.append(MyTableModelBig::parse_cell_data(cleaned_field));
            }

            // 使用优化的添加方法
            ui->ITV_csv->get_table_model()->append_row(row_data);
            row_count++;

            // 每1000行输出一次进度信息
            if (row_count % 1000 == 0) {
                qDebug() << "已加载" << row_count << "行数据";
            }
        }
    }

    file.close();
    qDebug() << "优化CSV文件加载完成，共" << row_count << "行数据";

    // 输出内存使用统计
    // ui->ITV_csv->get_table_model()->printMemoryStats();
}

