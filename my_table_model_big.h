﻿#ifndef MYOPTMIZEMODE_H
#define MYOPTMIZEMODE_H

#include <QObject>
#include <QAbstractTableModel>
#include <QVariant>
#include "io_custom_widgets_global.h"

// 数据类型枚举 - 使用更紧凑的编码
enum class CellDataType : quint8 {
    String = 0,
    Integer = 1,
    Double = 2,
    Boolean = 3,
    EmptyString = 4  // 空字符串特殊处理
};

// 超级优化的单元格数据结构 - 只占用16字节
struct CellData {
    CellDataType type : 8;  // 只用8位存储类型
    union {
        int int_value;
        double double_value;
        bool bool_value;
        quint32 string_index;  // 字符串池索引
    };

    CellData() : type(CellDataType::EmptyString), string_index(0) {}
    CellData(const QString& str);
    CellData(int val) : type(CellDataType::Integer), int_value(val) {}
    CellData(double val) : type(CellDataType::Double), double_value(val) {}
    CellData(bool val) : type(CellDataType::Boolean), bool_value(val) {}

    QString toString() const;
    QVariant toVariant() const;
};

// 字符串池 - 减少重复字符串的内存占用
class StringPool {
public:
    static StringPool& instance() {
        static StringPool pool;
        return pool;
    }

    quint32 addString(const QString& str) {
        if (str.isEmpty()) return 0;

        auto it = string_to_index_.find(str);
        if (it != string_to_index_.end()) {
            return it.value();
        }

        quint32 index = strings_.size();
        strings_.append(str);
        string_to_index_[str] = index;
        return index;
    }

    const QString& getString(quint32 index) const {
        static QString empty;
        if (index >= strings_.size()) return empty;
        return strings_[index];
    }

    void clear() {
        strings_.clear();
        string_to_index_.clear();
    }

    size_t getMemoryUsage() const {
        size_t total = 0;
        for (const QString& str : strings_) {
            total += str.size() * sizeof(QChar) + sizeof(QString);
        }
        return total;
    }

private:
    QVector<QString> strings_;
    QHash<QString, quint32> string_to_index_;
};

class IO_CUSTOM_WIDGETSSHARED_EXPORT MyOptmizeModel : public QAbstractTableModel
{
    Q_OBJECT
public:
    explicit MyOptmizeModel(QObject *parent = 0);

    int rowCount(const QModelIndex &parent = QModelIndex())const override;
    int columnCount(const QModelIndex &parent = QModelIndex())const override;
    QVariant data(const QModelIndex &index, int role = Qt::DisplayRole)const override;

    bool setData(const QModelIndex &index, const QVariant &value, int role = Qt::EditRole) override;
    QVariant headerData(int section, Qt::Orientation orientation, int role = Qt::DisplayRole) const override;
    bool insertRows(int row, int count, const QModelIndex &parent = QModelIndex()) override;
    bool removeRows(int row, int count, const QModelIndex &parent = QModelIndex());
    virtual Qt::ItemFlags flags(const QModelIndex &index) const override;

    void set_title_list(QVector<QString> titles);
    void append_row(const QVector<CellData>& row_data);  // 新增优化方法
    void delete_first_row();
    void delete_last_row();
    void delete_all_row();

    // 初始化方法
    void initialize_model();

    // 调试方法
    void debug_model_state() const;

    QVector<QString> get_title_list();
    QVector<QVector<QString>> get_model_data();

    // 新增：自动检测数据类型的方法
    static CellData parse_cell_data(const QString& text);

    // 内存使用统计
    size_t getMemoryUsage() const;
    void printMemoryStats() const;

    // 内存优化选项
    void enableUltraMemoryMode(bool enable = true);  // 启用超级内存模式
    void compactStringPool();  // 压缩字符串池

private:
    QVector<QVector<CellData>> model_data_optimized_;  // 优化的数据存储
    QVector<QString> model_title_list_;
    bool ultra_memory_mode_;      // 超级内存模式
};

#endif // MyOptmizeModel_H
